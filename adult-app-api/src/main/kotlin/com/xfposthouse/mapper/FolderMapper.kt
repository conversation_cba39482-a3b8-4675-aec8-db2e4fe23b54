package com.xfposthouse.mapper

import com.xfposthouse.module.space.entity.FolderEntity
import com.xfposthouse.module.space.entity.SpaceEntity
import org.apache.ibatis.annotations.*
import org.springframework.stereotype.Repository

@Repository
interface FolderMapper: BaseMapper<SpaceEntity>  {

    // 查询所有文件夹
    @Select("SELECT * FROM folders")
    fun findAll(): List<FolderEntity>

    @Select("SELECT * FROM folders WHERE user_id = #{userId}")
    fun findByUserId(userId: Int): List<FolderEntity>?

    @Select("SELECT * FROM folders WHERE user_id IN (#{userIds})")
    fun getFoldersByUserIds(@Param("userIds") userIds: String?): List<FolderEntity?>?

    // 根据 ID 查询文件夹
    @Select("SELECT * FROM folders WHERE id = #{id}")
    fun findById(id: Int): FolderEntity?

    // 插入新文件夹
    @Insert("INSERT INTO folders (user_id, name, create_time, last_modified) VALUES (#{userId}, #{name}, #{createTime}, #{lastModified})")
    fun insertFolder(folder: FolderEntity): Int

    // 更新文件夹信息
    @Update("UPDATE folders SET user_id = #{userId}, name = #{name}, create_time = #{createTime}, last_modified = #{lastModified} WHERE id = #{id}")
    fun update(folder: FolderEntity): Int

    // 删除文件夹
    @Delete("DELETE FROM folders WHERE id = #{id}")
    fun deleteById(id: Int): Int
}