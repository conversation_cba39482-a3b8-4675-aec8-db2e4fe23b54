package discipline

import entity.DisciplineNodeEntity
import entity.DisciplinePlanEntity
import mapper.DisciplineMapper
import mapper.DisciplineNodeMapper
import org.springframework.stereotype.Service

@Service
class DisciplineServer(
    private val planMapper: DisciplineMapper,
    private val nodeMapper: DisciplineNodeMapper,
) {
    fun createPlan(plan: DisciplinePlanEntity): Boolean {
        return planMapper.insert(plan) > 0
    }

    fun createPlanNodes(plans: List<DisciplineNodeEntity>): Boolean {
        return nodeMapper.insertBatch(plans) > 0
    }
}